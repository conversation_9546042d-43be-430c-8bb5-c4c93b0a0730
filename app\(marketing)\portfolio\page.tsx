"use client";

import ElementReveal from "@/components/animations/element-reveal";
import TextReveal from "@/components/animations/text-reveal";
import { ImageGallery } from "@/components/image-gallery";
import { Button } from "@/components/ui/button";
import Lenis from "@studio-freight/lenis";
import { ArrowRight } from "lucide-react";
import { motion } from "motion/react";
import Link from "next/link";
import { useEffect, useState } from "react";

const categories = [
   { id: "all", name: "All Work", count: 45 },
   { id: "wedding", name: "Weddings", count: 15 },
   { id: "pre-wedding", name: "Pre-Wedding", count: 9 },
   { id: "pregnancy", name: "Pregnancy", count: 9 },
   { id: "child-dedication", name: "Child Dedication", count: 9 },
   { id: "videography", name: "Videography", count: 1 },
   { id: "events", name: "Events", count: 2 },
];

const portfolioImages = {
   wedding: [
      {
         src: "/images/wedding-shoots/wedding-shoot-1.JPG",
         alt: "Wedding ceremony moment",
         category: "wedding",
      },
      {
         src: "/images/wedding-shoots/wedding-shoot-2.PNG",
         alt: "Bride and groom portrait",
         category: "wedding",
      },
      {
         src: "/images/wedding-shoots/wedding-shoot-3.JPG",
         alt: "Wedding reception",
         category: "wedding",
      },
      {
         src: "/images/wedding-shoots/wedding-shoot-4.JPG",
         alt: "Wedding details",
         category: "wedding",
      },
      {
         src: "/images/wedding-shoots/wedding-shoot-5.JPG",
         alt: "Wedding party",
         category: "wedding",
      },
      {
         src: "/images/wedding-shoots/wedding-shoot-6.JPG",
         alt: "Wedding venue",
         category: "wedding",
      },
      {
         src: "/images/wedding-shoots/wedding-shoot-7.JPG",
         alt: "Wedding couple",
         category: "wedding",
      },
      {
         src: "/images/wedding-shoots/wedding-shoot-8.JPG",
         alt: "Wedding celebration",
         category: "wedding",
      },
      {
         src: "/images/wedding-shoots/wedding-shoot-9.JPG",
         alt: "Wedding photography",
         category: "wedding",
      },
      {
         src: "/images/wedding-shoots/wedding-shoot-10.JPG",
         alt: "Wedding moments",
         category: "wedding",
      },
      {
         src: "/images/wedding-shoots/wedding-shoot-11.jpg",
         alt: "Wedding day",
         category: "wedding",
      },
      {
         src: "/images/wedding-shoots/wedding-shoot-12.PNG",
         alt: "Wedding ceremony",
         category: "wedding",
      },
   ],
   "pre-wedding": [
      {
         src: "/images/pre-wedding-shoots/pre-wedding-shoot-1.JPG",
         alt: "Pre-wedding couple portrait",
         category: "pre-wedding",
      },
      {
         src: "/images/pre-wedding-shoots/pre-wedding-shoot-2.JPG",
         alt: "Engagement session outdoors",
         category: "pre-wedding",
      },
      {
         src: "/images/pre-wedding-shoots/pre-wedding-shoot-3.JPG",
         alt: "Romantic couple photography",
         category: "pre-wedding",
      },
      {
         src: "/images/pre-wedding-shoots/pre-wedding-shoot-4.JPG",
         alt: "Pre-wedding lifestyle shoot",
         category: "pre-wedding",
      },
      {
         src: "/images/pre-wedding-shoots/pre-wedding-shoot-5.JPG",
         alt: "Couple in natural setting",
         category: "pre-wedding",
      },
   ],
   pregnancy: [
      {
         src: "/images/pregnancy-shoots/pregnancy-shoot-1.jpg",
         alt: "Maternity photography session",
         category: "pregnancy",
      },
      {
         src: "/images/pregnancy-shoots/pregnancy-shoot-2.JPG",
         alt: "Pregnancy portrait outdoors",
         category: "pregnancy",
      },
      {
         src: "/images/pregnancy-shoots/pregnancy-shoot-3.jpg",
         alt: "Expecting mother photography",
         category: "pregnancy",
      },
      {
         src: "/images/pregnancy-shoots/pregnancy-shoot-4.jpg",
         alt: "Maternity couple session",
         category: "pregnancy",
      },
      {
         src: "/images/pregnancy-shoots/pregnancy-shoot-5.jpg",
         alt: "Pregnancy photography studio",
         category: "pregnancy",
      },
      {
         src: "/images/pregnancy-shoots/pregnancy-shoot-6.JPG",
         alt: "Maternity lifestyle photography",
         category: "pregnancy",
      },
   ],
   birthday: [
      {
         src: "/images/birthday-shoots/birthday-shoot-1.JPG",
         alt: "Child dedication ceremony",
         category: "birthday-shoot",
      },
      {
         src: "/images/birthday-shoots/birthday-shoot-2.JPG",
         alt: "Family during dedication",
         category: "birthday-shoot",
      },
      {
         src: "/images/birthday-shoots/birthday-shoot-3.JPG",
         alt: "Child dedication portraits",
         category: "birthday-shoot",
      },
      {
         src: "/images/birthday-shoots/birthday-shoot-4.JPG",
         alt: "Dedication ceremony moments",
         category: "birthday-shoot",
      },
      {
         src: "/images/birthday-shoots/birthday-shoot-5.JPG",
         alt: "Family celebration",
         category: "birthday-shoot",
      },
      {
         src: "/images/birthday-shoots/birthday-shoot-6.JPG",
         alt: "Child dedication photography",
         category: "birthday-shoot",
      },
   ],
   "child-dedication": [
      {
         src: "/images/child-dedication/child-dedication-1.PNG",
         alt: "Child dedication ceremony",
         category: "child-dedication",
      },
   ],
   "bridal-shower": [
      {
         src: "/images/bridal-shower/bridal-shower-1.JPG",
         alt: "Bridal shower",
         category: "bridal-shower",
      },
      {
         src: "/images/bridal-shower/bridal-shower-2.JPG",
         alt: "Bridal shower",
         category: "bridal-shower",
      },
      {
         src: "/images/bridal-shower/bridal-shower-3.JPG",
         alt: "Bridal shower",
         category: "bridal-shower",
      },
   ],
};

const getAllImages = () => {
   return Object.values(portfolioImages).flat();
};

export default function PortfolioPage() {
   const [activeCategory, setActiveCategory] = useState("all");

   useEffect(() => {
      const lenis = new Lenis();

      function raf(time: number) {
         lenis.raf(time);
         requestAnimationFrame(raf);
      }

      requestAnimationFrame(raf);
   });

   const getFilteredImages = () => {
      if (activeCategory === "all") {
         return getAllImages();
      }
      return (
         portfolioImages[activeCategory as keyof typeof portfolioImages] || []
      );
   };

   const filteredImages = getFilteredImages();

   return (
      <div className="min-h-screen">
         {/* Hero Section */}
         <section className="pt-36 pb-16 bg-gradient-hero">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
               <TextReveal>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-18">
                     Our <span className="text-primary">Portfolio</span>
                  </h1>
               </TextReveal>
               <TextReveal className="mb-8">
                  <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                     Explore our collection of beautiful photography and
                     videography work. Each image tells a story, captures an
                     emotion, and preserves a precious moment in time.
                  </p>
               </TextReveal>
               <ElementReveal>
                  <Button asChild size="lg">
                     <Link href="/contact">Start Your Project</Link>
                  </Button>
               </ElementReveal>
            </div>
         </section>

         {/* Filter Categories */}
         <section className="py-12 bg-astral-grey">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="flex flex-wrap justify-center gap-4">
                  {categories.map((category) => (
                     <motion.div
                        key={category.id}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3 }}
                        viewport={{ once: true }}
                     >
                        <Button
                           key={category.id}
                           size={"lg"}
                           variant={
                              activeCategory === category.id
                                 ? "default"
                                 : "outline"
                           }
                           onClick={() => setActiveCategory(category.id)}
                           className={`flex items-center gap-2 ${
                              activeCategory === category.id
                                 ? "bg-gradient-accent"
                                 : ""
                           }`}
                        >
                           {category.name}
                        </Button>
                     </motion.div>
                  ))}
               </div>
            </div>
         </section>

         {/* Gallery */}
         <section className="py-20 pt-12">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-12">
                  <TextReveal>
                     <h2 className="text-2xl font-bold mb-4">
                        {categories.find((cat) => cat.id === activeCategory)
                           ?.name || "All Work"}
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-muted-foreground">
                        {filteredImages.length}{" "}
                        {filteredImages.length === 1 ? "image" : "images"} in
                        this category
                     </p>
                  </TextReveal>
               </div>

               <ImageGallery images={filteredImages} />
            </div>
         </section>

         {/* CTA Section */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4 text-center">
               <div className="max-w-3xl mx-auto">
                  <TextReveal>
                     <h2 className="text-4xl font-playfair font-bold text-foreground mb-6">
                        Let&apos;s Create Your{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Next Masterpiece{" "}
                        </span>
                     </h2>
                  </TextReveal>
                  <TextReveal className="mb-8">
                     <p className="text-lg text-muted-foreground font-montserrat leading-relaxed">
                        Let&apos;s create something beautiful together. Contact
                        us to discuss your photography and videography needs.
                     </p>
                  </TextReveal>
                  <ElementReveal>
                     <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                        <Button
                           asChild
                           size="lg"
                           className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg px-8"
                        >
                           <Link href="/contact">
                              Contact Now{" "}
                              <ArrowRight className="ml-2 h-5 w-5" />
                           </Link>
                        </Button>
                        <Button
                           asChild
                           variant="outline"
                           size="lg"
                           className="border-primary/20 hover:bg-primary/5 font-montserrat font-semibold text-lg px-8"
                        >
                           <Link href="/services">View Services</Link>
                        </Button>
                     </div>
                  </ElementReveal>
               </div>
            </div>
         </section>
      </div>
   );
}
