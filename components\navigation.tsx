"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
   Sheet,
   SheetClose,
   Sheet<PERSON>ontent,
   Sheet<PERSON><PERSON><PERSON>,
   SheetTrigger,
} from "@/components/ui/sheet";
import { Bars3BottomRightIcon, XMarkIcon } from "@heroicons/react/24/solid";
import { AnimatePresence, motion } from "framer-motion";
import { ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

const navigationItems = [
   { name: "Home", href: "/" },
   { name: "About", href: "/about" },
   {
      name: "Services",
      href: "/services",
      submenu: [
         { name: "Wedding Photography", href: "/services/wedding" },
         { name: "Pre-Wedding Shoots", href: "/services/pre-wedding" },
         { name: "Pregnancy Photography", href: "/services/pregnancy" },
         { name: "Child Dedication", href: "/services/child-dedication" },
         { name: "Birthday Shoots", href: "/services/birthday-shoot" },
         { name: "Bridal Shower", href: "/services/bridal-shower" },
         { name: "Videography", href: "/services/videography" },
         { name: "360 Video Booth", href: "/services/360-booth" },
         { name: "Dry Ice Machine", href: "/services/dry-ice" },
      ],
   },
   {
      name: "Gallery",
      href: "/gallery",
      submenu: [
         { name: "All Galleries", href: "/gallery" },
         { name: "Albums", href: "/gallery/albums" },
         { name: "Collections", href: "/gallery/collections" },
      ],
   },
   { name: "Portfolio", href: "/portfolio" },
   { name: "Contact", href: "/contact" },
];

export function Navigation() {
   const [isOpen, setIsOpen] = useState(false);
   const [expandedItems, setExpandedItems] = useState<string[]>([]);
   const pathname = usePathname();

   const isActive = (href: string) => {
      if (href === "/") {
         return pathname === "/";
      }
      return pathname.startsWith(href);
   };

   const toggleExpanded = (itemName: string) => {
      setExpandedItems((prev) =>
         prev.includes(itemName)
            ? prev.filter((name) => name !== itemName)
            : [...prev, itemName]
      );
   };

   const handleSheetClose = () => {
      setIsOpen(false);
      setExpandedItems([]);
   };

   const containerVariants = {
      hidden: { opacity: 0 },
      visible: {
         opacity: 1,
         transition: {
            staggerChildren: 0.1,
            delayChildren: 0.2,
         },
      },
      exit: {
         opacity: 0,
         transition: {
            staggerChildren: 0.05,
            staggerDirection: -1,
         },
      },
   };

   const itemVariants = {
      hidden: {
         opacity: 0,
         x: 20,
         scale: 0.95,
      },
      visible: {
         opacity: 1,
         x: 0,
         scale: 1,
         transition: {
            type: "spring" as const,
            stiffness: 300,
            damping: 24,
         },
      },
      exit: {
         opacity: 0,
         x: -20,
         scale: 0.95,
         transition: {
            duration: 0.2,
         },
      },
   };

   const submenuVariants = {
      hidden: {
         opacity: 0,
         height: 0,
         scaleY: 0,
      },
      visible: {
         opacity: 1,
         height: "auto",
         scaleY: 1,
         transition: {
            type: "spring" as const,
            stiffness: 300,
            damping: 30,
         },
      },
      exit: {
         opacity: 0,
         height: 0,
         scaleY: 0,
         transition: {
            duration: 0.2,
         },
      },
   };

   const submenuItemVariants = {
      hidden: {
         opacity: 0,
         x: 20,
         scale: 0.95,
      },
      visible: {
         opacity: 1,
         x: 0,
         scale: 1,
         transition: {
            type: "spring" as const,
            stiffness: 300,
            damping: 24,
         },
      },
      exit: {
         opacity: 0,
         x: -20,
         scale: 0.95,
         transition: {
            duration: 0.2,
         },
      },
   };

   return (
      <motion.header
         initial={{ y: -100, opacity: 0 }}
         animate={{ y: 0, opacity: 1 }}
         transition={{ duration: 0.5 }}
         className="fixed top-0 left-0 right-0 z-50 bg-background/85 backdrop-blur-sm border-b border-astral-grey-light"
      >
         <div className="container mx-auto py-3 px-4 sm:px-6 lg:px-8 relative">
            <div className="flex h-16 items-center justify-between">
               {/* Logo */}
               <Link href="/" className="flex items-center space-x-2">
                  <Image
                     src="/astral-logo.svg"
                     alt="Astral logo"
                     width={100}
                     height={50}
                  />
               </Link>

               {/* Desktop Navigation */}
               <motion.nav
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 }}
                  className="hidden md:flex items-center absolute left-[50%] translate-x-[-50%] space-x-8"
               >
                  {navigationItems.map((item, index) => (
                     <motion.div
                        key={item.name}
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: 0.5 + index * 0.1 }}
                        className="relative group"
                     >
                        <Link
                           href={item.href}
                           className={`text-base font-medium transition-colors hover:text-primary ${
                              isActive(item.href)
                                 ? "text-primary font-semibold"
                                 : "text-muted-foreground"
                           }`}
                        >
                           {item.name}
                        </Link>

                        {/* Submenu for Services */}
                        {item.submenu && (
                           <div className="absolute top-full left-0 mt-2 w-56 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 bg-background border rounded-md shadow-lg overflow-hidden">
                              {item.submenu.map((subItem) => (
                                 <Link
                                    key={subItem.name}
                                    href={subItem.href}
                                    className="block px-4 py-3 text-sm text-muted-foreground hover:text-white hover:bg-astral-grey-light transition-colors"
                                 >
                                    {subItem.name}
                                 </Link>
                              ))}
                           </div>
                        )}
                     </motion.div>
                  ))}
               </motion.nav>

               <div className="px-4 hidden md:flex">
                  <Button
                     asChild
                     variant="default"
                     className="w-full h-auto py-3 px-6 bg-gradient-accent hover:opacity-90 font-semibold"
                  >
                     <Link href="/contact">Book Now</Link>
                  </Button>
               </div>

               {/* Mobile Navigation */}
               <Sheet open={isOpen} onOpenChange={setIsOpen}>
                  <SheetTrigger asChild className="md:hidden">
                     <Button
                        variant="ghost"
                        size="icon"
                        className="hover:bg-transparent"
                     >
                        <Bars3BottomRightIcon className="size-7" />
                        <span className="sr-only">Toggle menu</span>
                     </Button>
                  </SheetTrigger>
                  <SheetContent
                     side="top"
                     className="w-full h-full overflow-y-auto bg-background/95 backdrop-blur-sm p-8"
                     showCloseButton={false}
                  >
                     <SheetClose asChild>
                        <Button
                           variant="ghost"
                           size="icon"
                           className="hover:bg-transparent absolute top-4 right-4"
                        >
                           <XMarkIcon className="size-7" />
                           <span className="sr-only">Close menu</span>
                        </Button>
                     </SheetClose>
                     <SheetTitle className="sr-only">Menu</SheetTitle>

                     {/* Logo */}
                     <motion.div
                        className="flex justify-center mb-3"
                        initial={{ opacity: 0, y: -20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2, duration: 0.4 }}
                     >
                        <Link href="/" onClick={handleSheetClose}>
                           <Image
                              src="/astral-logo.svg"
                              alt="Astral Studios"
                              width={120}
                              height={60}
                              className="h-12 w-auto"
                           />
                        </Link>
                     </motion.div>

                     {/* Mobile Navigation Content */}
                     <motion.div
                        className="flex flex-col space-y-2"
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                     >
                        {navigationItems.map((item) => (
                           <motion.div
                              key={item.name}
                              variants={itemVariants}
                              className="border-b border-border/50 last:border-b-0"
                           >
                              {item.submenu ? (
                                 <div>
                                    <div className="flex items-center justify-between">
                                       <Link
                                          href={item.href}
                                          className={`flex-1 py-4 px-2 text-lg font-medium transition-colors hover:text-primary ${
                                             isActive(item.href)
                                                ? "text-primary font-semibold"
                                                : "text-muted-foreground"
                                          }`}
                                          onClick={handleSheetClose}
                                       >
                                          {item.name}
                                       </Link>
                                       <button
                                          onClick={(e) => {
                                             e.preventDefault();
                                             e.stopPropagation();
                                             toggleExpanded(item.name);
                                          }}
                                          className="p-4 transition-colors"
                                          aria-label={`Toggle ${item.name} submenu`}
                                       >
                                          <motion.div
                                             animate={{
                                                rotate: expandedItems.includes(
                                                   item.name
                                                )
                                                   ? 90
                                                   : 0,
                                             }}
                                             transition={{ duration: 0.2 }}
                                          >
                                             <ChevronRight className="h-5 w-5" />
                                          </motion.div>
                                       </button>
                                    </div>

                                    <AnimatePresence>
                                       {expandedItems.includes(item.name) && (
                                          <motion.div
                                             variants={submenuVariants}
                                             initial="hidden"
                                             animate="visible"
                                             exit="exit"
                                             className="overflow-hidden"
                                          >
                                             <div className="space-y-1 py-2">
                                                {item.submenu.map((subItem) => (
                                                   <motion.div
                                                      key={subItem.name}
                                                      variants={
                                                         submenuItemVariants
                                                      }
                                                      initial="hidden"
                                                      animate="visible"
                                                      exit="exit"
                                                   >
                                                      <Link
                                                         href={subItem.href}
                                                         className={`block py-3 px-6 text-base font-medium transition-colors hover:text-primary border-l-2 border-transparent hover:border-primary/20 ${
                                                            isActive(
                                                               subItem.href
                                                            )
                                                               ? "text-primary font-semibold border-primary/40 bg-primary/5"
                                                               : "text-muted-foreground"
                                                         }`}
                                                         onClick={
                                                            handleSheetClose
                                                         }
                                                      >
                                                         {subItem.name}
                                                      </Link>
                                                   </motion.div>
                                                ))}
                                             </div>
                                          </motion.div>
                                       )}
                                    </AnimatePresence>
                                 </div>
                              ) : (
                                 <Link
                                    href={item.href}
                                    className={`block py-4 px-2 text-lg font-medium transition-colors hover:text-primary ${
                                       isActive(item.href)
                                          ? "text-primary font-semibold"
                                          : "text-muted-foreground"
                                    }`}
                                    onClick={handleSheetClose}
                                 >
                                    {item.name}
                                 </Link>
                              )}
                           </motion.div>
                        ))}

                        {/* CTA Button */}
                        <motion.div variants={itemVariants}>
                           <Button
                              asChild
                              className="w-full bg-gradient-accent hover:opacity-90 font-semibold h-auto py-3"
                              onClick={handleSheetClose}
                           >
                              <Link href="/contact">Book Now</Link>
                           </Button>
                        </motion.div>
                     </motion.div>
                  </SheetContent>
               </Sheet>
            </div>
         </div>
      </motion.header>
   );
}
