"use client";

import ElementReveal from "@/components/animations/element-reveal";
import StaggerReveal from "@/components/animations/stagger-reveal";
import TextReveal from "@/components/animations/text-reveal";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { featuredServices } from "@/lib/data";
import Lenis from "@studio-freight/lenis";
import { motion } from "framer-motion";
import { ArrowRight, Check, Sparkles, Video, Wind } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

export const videographyServices = [
   {
      title: "Wedding Films",
      description:
         "Cinematic wedding films capturing the emotion and beauty of your special day.",
      features: [
         "Full ceremony coverage",
         "Reception highlights",
         "Professional editing",
         "HD/4K quality",
      ],
   },
   {
      title: "Pre-wedding Videos",
      description:
         "Romantic pre-wedding video sessions showcasing your love story.",
      features: [
         "Multiple locations",
         "Story-driven narrative",
         "Drone footage available",
         "Music synchronization",
      ],
   },
   {
      title: "Event Documentation",
      description:
         "Professional event videography for special occasions and celebrations.",
      features: [
         "Multi-camera setup",
         "Live streaming options",
         "Professional audio",
         "Quick turnaround",
      ],
   },
   {
      title: "Highlight Reels",
      description:
         "Short, impactful highlight videos perfect for social media sharing.",
      features: [
         "60-90 second duration",
         "Social media optimized",
         "Dynamic editing",
         "Multiple formats",
      ],
   },
];

export const specialServices = [
   {
      title: "360 Video Booth",
      description:
         "Create stunning 360-degree videos that capture your guests from every angle. Our interactive video booth is perfect for weddings, parties, and corporate events, providing entertainment and memorable keepsakes for your guests.",
      features: [
         "Professional setup",
         "High-quality cameras",
         "Instant social sharing",
         "Custom branding",
         "Props included",
         "Professional attendant",
      ],
      icon: Sparkles,
      ctaText: "Book the 360 Booth",
      href: "/contact",
   },
   {
      title: "Dry Ice Machine",
      description:
         "Add dramatic flair to your event with our professional dry ice machine. Perfect for first dances, grand entrances, and creating magical atmospheric effects that will leave your guests in awe and provide stunning photo opportunities.",
      features: [
         "Safe operation",
         "Dramatic effects",
         "Perfect for first dances",
         "Professional grade",
         "Venue approved",
         "Stunning visuals",
      ],
      icon: Wind,
      ctaText: "Add Dry Ice Effects",
      href: "/contact",
   },
];

const Services = () => {
   const [hoveredCard, setHoveredCard] = useState<number | null>(null);

   useEffect(() => {
      const lenis = new Lenis();

      function raf(time: number) {
         lenis.raf(time);
         requestAnimationFrame(raf);
      }

      requestAnimationFrame(raf);
   });

   return (
      <div className="min-h-screen bg-background">
         {/* Hero Section */}
         <section className="pt-36 pb-16 bg-gradient-hero">
            <div className="container mx-auto px-4">
               <div className="text-center max-w-4xl mx-auto">
                  <TextReveal>
                     <h1 className="text-5xl md:text-6xl font-playfair font-bold text-foreground mb-6">
                        Our{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Services
                        </span>
                     </h1>
                  </TextReveal>
                  <TextReveal className="mb-8">
                     <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                        From intimate moments to grand celebrations, we offer
                        comprehensive photography and videography services
                        tailored to capture your unique story.
                     </p>
                  </TextReveal>
                  <ElementReveal>
                     <Button asChild size="lg">
                        <Link href="/contact">Book Now</Link>
                     </Button>
                  </ElementReveal>
               </div>
            </div>
         </section>

         <section className="py-20 bg-astral-grey relative">
            <div className="container max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6 leading-18">
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Photography
                        </span>{" "}
                        Services
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground font-montserrat max-w-3xl mx-auto">
                        Professional photography services tailored to capture
                        the essence and emotion of your most important moments.
                     </p>
                  </TextReveal>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {featuredServices.map((service, index) => {
                     return (
                        <motion.div
                           key={service.title}
                           initial={{ opacity: 0, scale: 0.9 }}
                           whileInView={{ opacity: 1, scale: 1 }}
                           transition={{ duration: 0.6, delay: index * 0.1 }}
                           viewport={{ once: true }}
                           onHoverStart={() => setHoveredCard(index)}
                           onHoverEnd={() => setHoveredCard(null)}
                           className="group"
                        >
                           <Card className="relative p-0 overflow-hidden bg-card/80 backdrop-blur-sm border-astral-grey-light hover:border-primary/50 transition-all duration-500 h-full">
                              {/* Image with overlay */}
                              <div className="relative aspect-[4/3] overflow-hidden">
                                 <Image
                                    src={service.image}
                                    alt={service.title}
                                    fill
                                    className="object-cover transition-all duration-700 group-hover:scale-105 group-hover:brightness-110"
                                 />

                                 {/* Animated overlay */}
                                 <motion.div
                                    className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"
                                    animate={{
                                       opacity:
                                          hoveredCard === index ? 0.9 : 0.6,
                                    }}
                                    transition={{ duration: 0.3 }}
                                 />
                              </div>

                              <CardContent className="p-6 pt-2">
                                 <h3 className="text-xl font-bold text-foreground mb-3 group-hover:text-primary transition-colors duration-300">
                                    {service.title}
                                 </h3>

                                 <p className="text-muted-foreground text-sm mb-4 leading-relaxed line-clamp-3">
                                    {service.description}
                                 </p>

                                 {/* Features with animated appearance */}
                                 <div className="space-y-2 mb-6">
                                    {service.features
                                       .slice(0, 3)
                                       .map((feature, idx) => (
                                          <StaggerReveal
                                             key={feature}
                                             index={idx}
                                             className="flex items-center gap-2 text-xs text-muted-foreground"
                                          >
                                             <div className="w-2 h-2 bg-primary rounded-full" />
                                             <span>{feature}</span>
                                          </StaggerReveal>
                                       ))}
                                 </div>

                                 <Button
                                    asChild
                                    size="sm"
                                    className="w-full transition-all duration-300 group-hover:shadow-glow py-3 h-auto"
                                 >
                                    <Link href={service.href}>
                                       <motion.span
                                          animate={{
                                             x: hoveredCard === index ? 5 : 0,
                                          }}
                                          transition={{ duration: 0.2 }}
                                       >
                                          Learn More
                                       </motion.span>
                                       <ArrowRight className="h-4 w-4 ml-2" />
                                    </Link>
                                 </Button>
                              </CardContent>
                           </Card>
                        </motion.div>
                     );
                  })}
               </div>
            </div>
         </section>

         {/* Videography Services */}
         <section className="py-20 bg-background">
            <div className="container mx-auto px-4">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6 leading-18">
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Videography
                        </span>{" "}
                        Services
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground font-montserrat max-w-3xl mx-auto">
                        Cinematic video production that brings your stories to
                        life with professional quality and creative vision.
                     </p>
                  </TextReveal>
               </div>

               <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {videographyServices.map((service, index) => (
                     <motion.div
                        key={service.title}
                        initial={{ opacity: 0, y: 20 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: 0.1 * index }}
                        viewport={{ once: true }}
                        className="group"
                     >
                        <Card className="bg-card border-astral-grey-light hover:shadow-card transition-all text-center p-0">
                           <CardContent className="p-8">
                              <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center mx-auto mb-6">
                                 <Video className="h-8 w-8 text-white" />
                              </div>
                              <h3 className="text-xl font-playfair font-semibold text-foreground mb-4">
                                 {service.title}
                              </h3>
                              <p className="text-muted-foreground font-montserrat mb-4">
                                 {service.description}
                              </p>
                              <ul className="text-sm text-muted-foreground font-montserrat space-y-2 mb-6">
                                 {service.features.map((feature) => (
                                    <li key={feature}>• {feature}</li>
                                 ))}
                              </ul>
                           </CardContent>
                        </Card>
                     </motion.div>
                  ))}
               </div>
            </div>
         </section>

         {/* Special Services */}
         <section className="py-20 bg-astral-grey">
            <div className="container mx-auto px-4">
               <div className="text-center mb-16">
                  <TextReveal>
                     <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6 leading-18">
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Special
                        </span>{" "}
                        Services
                     </h2>
                  </TextReveal>
                  <TextReveal>
                     <p className="text-lg text-muted-foreground font-montserrat max-w-3xl mx-auto">
                        Unique additions to make your event extraordinary and
                        unforgettable.
                     </p>
                  </TextReveal>
               </div>

               <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                  {specialServices.map((service, index) => {
                     const Icon = service.icon;
                     const firstColumn = service.features.slice(0, 3);
                     const secondColumn = service.features.slice(3);
                     return (
                        <motion.div
                           key={service.title}
                           initial={{ opacity: 0, y: 20 }}
                           whileInView={{ opacity: 1, y: 0 }}
                           transition={{ duration: 0.5, delay: 0.1 * index }}
                           viewport={{ once: true }}
                        >
                           <Card className="bg-card p-0 border-astral-grey-light hover:shadow-card transition-all">
                              <CardContent className="p-8">
                                 <div className="flex items-center space-x-4 mb-6">
                                    <div className="w-16 h-16 bg-gradient-accent rounded-full flex items-center justify-center">
                                       <Icon className="h-8 w-8 text-white" />
                                    </div>
                                    <h3 className="text-3xl font-playfair font-bold text-foreground">
                                       {service.title}
                                    </h3>
                                 </div>
                                 <p className="text-lg text-muted-foreground font-montserrat mb-6 leading-relaxed">
                                    {service.description}
                                 </p>
                                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                    <div className="space-y-3">
                                       {firstColumn.map((feature, idx) => (
                                          <StaggerReveal
                                             key={feature}
                                             index={idx}
                                             className="flex items-center space-x-3"
                                          >
                                             <Check className="h-5 w-5 text-primary flex-shrink-0" />
                                             <span className="text-muted-foreground font-montserrat">
                                                {feature}
                                             </span>
                                          </StaggerReveal>
                                       ))}
                                    </div>
                                    <div className="space-y-3">
                                       {secondColumn.map((feature, idx) => (
                                          <StaggerReveal
                                             key={feature}
                                             index={idx}
                                             className="flex items-center space-x-3"
                                          >
                                             <Check className="h-5 w-5 text-primary flex-shrink-0" />
                                             <span className="text-muted-foreground font-montserrat">
                                                {feature}
                                             </span>
                                          </StaggerReveal>
                                       ))}
                                    </div>
                                 </div>
                                 <Button
                                    asChild
                                    className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold"
                                 >
                                    <Link href={service.href}>
                                       {service.ctaText}
                                    </Link>
                                 </Button>
                              </CardContent>
                           </Card>
                        </motion.div>
                     );
                  })}
               </div>
            </div>
         </section>

         {/* Call to Action */}
         <section className="py-20 bg-background">
            <div className="container mx-auto px-4 text-center">
               <div className="max-w-3xl mx-auto">
                  <TextReveal>
                     <h2 className="text-4xl md:text-5xl font-playfair font-bold text-foreground mb-6 leading-18">
                        Ready to Book{" "}
                        <span className="bg-gradient-accent bg-clip-text text-transparent">
                           Your Service?
                        </span>
                     </h2>
                  </TextReveal>
                  <TextReveal className="mb-8">
                     <p className="text-lg text-muted-foreground font-montserrat leading-relaxed">
                        Let&apos;s discuss your photography and videography
                        needs. We&apos;ll work with you to create a customized
                        package that perfectly captures your special moments.
                     </p>
                  </TextReveal>
                  <ElementReveal>
                     <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                        <Button
                           asChild
                           size="lg"
                           className="bg-gradient-accent hover:opacity-90 font-montserrat font-semibold text-lg px-8"
                        >
                           <Link href="/contact">
                              Contact Now{" "}
                              <ArrowRight className="ml-2 h-5 w-5" />
                           </Link>
                        </Button>
                        <Button
                           asChild
                           variant="outline"
                           size="lg"
                           className="border-primary/20 hover:bg-primary/5 font-montserrat font-semibold text-lg px-8"
                        >
                           <Link href="/portfolio">View Portfolio</Link>
                        </Button>
                     </div>
                  </ElementReveal>
               </div>
            </div>
         </section>
      </div>
   );
};

export default Services;
